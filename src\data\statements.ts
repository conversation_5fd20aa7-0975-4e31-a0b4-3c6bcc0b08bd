import type { CompassTile, Quadrant } from '../types';

// Political Compass Quadrants
export const quadrants: Quadrant[] = [
  {
    name: "Authoritarian Left",
    color: "#dc3545", // Red
    description: "State control of economy, social restrictions",
    economicRange: [-10, 0],
    socialRange: [0, 10]
  },
  {
    name: "Authoritarian Right",
    color: "#007bff", // Blue
    description: "Free market, social restrictions",
    economicRange: [0, 10],
    socialRange: [0, 10]
  },
  {
    name: "Libertarian Left",
    color: "#28a745", // Green
    description: "Social programs, personal freedom",
    economicRange: [-10, 0],
    socialRange: [-10, 0]
  },
  {
    name: "Libertarian Right",
    color: "#ffc107", // Yellow
    description: "Free market, personal freedom",
    economicRange: [0, 10],
    socialRange: [-10, 0]
  }
];

// Political Compass Tiles with coordinate values - Fixed positioning
// Grid layout: 7x7 = 49 tiles total for comprehensive coverage
export const compassTiles: CompassTile[] = [
  // Authoritarian Left Quadrant (Red) - Top Left (economic < 0, social > 0)
  { id: '1', text: 'Strong leadership is necessary for order', economicValue: -3, socialValue: 8, isSelected: false },
  { id: '2', text: 'Workers should own the means of production', economicValue: -8, socialValue: 4, isSelected: false },
  { id: '3', text: 'Nationalize essential industries', economicValue: -9, socialValue: 6, isSelected: false },
  { id: '4', text: 'Redistribute wealth for equality', economicValue: -7, socialValue: 3, isSelected: false },
  { id: '5', text: 'The state should guide moral behavior', economicValue: -5, socialValue: 7, isSelected: false },
  { id: '6', text: 'National security justifies surveillance', economicValue: -4, socialValue: 9, isSelected: false },
  { id: '7', text: 'Merit-based immigration only', economicValue: 0, socialValue: 5, isSelected: false },

  // Authoritarian Right Quadrant (Blue) - Top Right (economic > 0, social > 0)
  { id: '8', text: 'Strong military protects freedom', economicValue: 5, socialValue: 7, isSelected: false },
  { id: '9', text: 'Traditional values should be preserved', economicValue: 6, socialValue: 6, isSelected: false },
  { id: '10', text: 'Respect for law and order', economicValue: 5, socialValue: 8, isSelected: false },
  { id: '11', text: 'Patriotism should be taught in schools', economicValue: 4, socialValue: 7, isSelected: false },
  { id: '12', text: 'Traditional family structures are best', economicValue: 3, socialValue: 8, isSelected: false },
  { id: '13', text: 'Compromise is better than extremism', economicValue: 8, socialValue: 8, isSelected: false },

  // Libertarian Left Quadrant (Green) - Bottom Left (economic < 0, social < 0)
  { id: '14', text: 'Universal basic income would help society', economicValue: -7, socialValue: -5, isSelected: false },
  { id: '15', text: 'Healthcare is a human right', economicValue: -6, socialValue: -4, isSelected: false },
  { id: '16', text: 'Climate change requires government action', economicValue: -5, socialValue: -3, isSelected: false },
  { id: '17', text: 'Housing is a human right', economicValue: -8, socialValue: -6, isSelected: false },
  { id: '18', text: 'Free education through college', economicValue: -6, socialValue: -7, isSelected: false },
  { id: '19', text: 'Unions protect workers', economicValue: -4, socialValue: -5, isSelected: false },

  // Libertarian Right Quadrant (Yellow) - Bottom Right (economic > 0, social < 0)
  { id: '20', text: 'Taxation is theft', economicValue: 8, socialValue: -7, isSelected: false },
  { id: '21', text: 'Free markets solve most problems', economicValue: 7, socialValue: -5, isSelected: false },
  { id: '22', text: 'Drug prohibition should be ended', economicValue: 3, socialValue: -8, isSelected: false },
  { id: '23', text: 'Private property rights are sacred', economicValue: 6, socialValue: -4, isSelected: false },
  { id: '24', text: 'Government surveillance violates privacy', economicValue: 2, socialValue: -9, isSelected: false },
  { id: '25', text: 'Abolish the minimum wage', economicValue: 8, socialValue: -3, isSelected: false },
  // Additional Center and Mixed Position Tiles
  { id: '26', text: 'Some regulation is necessary', economicValue: -2, socialValue: 2, isSelected: false },
  { id: '27', text: 'Balance individual and collective needs', economicValue: 0, socialValue: 0, isSelected: false },
  { id: '28', text: 'Mixed economy works best', economicValue: -1, socialValue: 1, isSelected: false },
  { id: '29', text: 'Moderate social policies', economicValue: 1, socialValue: -1, isSelected: false },
  { id: '30', text: 'Pragmatic solutions over ideology', economicValue: 0, socialValue: 0, isSelected: false },

  // More Authoritarian Left (Red) - Top Left
  { id: '31', text: 'Central planning prevents inequality', economicValue: -8, socialValue: 5, isSelected: false },
  { id: '32', text: 'Collective ownership benefits all', economicValue: -9, socialValue: 3, isSelected: false },
  { id: '33', text: 'State education shapes society', economicValue: -6, socialValue: 8, isSelected: false },

  // More Authoritarian Right (Blue) - Top Right
  { id: '34', text: 'Strong borders protect culture', economicValue: 7, socialValue: 9, isSelected: false },
  { id: '35', text: 'Corporate responsibility matters', economicValue: 4, socialValue: 5, isSelected: false },
  { id: '36', text: 'National identity is important', economicValue: 6, socialValue: 8, isSelected: false },

  // More Libertarian Left (Green) - Bottom Left
  { id: '37', text: 'Worker cooperatives are ideal', economicValue: -7, socialValue: -6, isSelected: false },
  { id: '38', text: 'Environmental protection is essential', economicValue: -5, socialValue: -2, isSelected: false },
  { id: '39', text: 'Social justice requires action', economicValue: -8, socialValue: -4, isSelected: false },

  // More Libertarian Right (Yellow) - Bottom Right
  { id: '40', text: 'Voluntary exchange benefits all', economicValue: 9, socialValue: -6, isSelected: false },
  { id: '41', text: 'Individual responsibility is key', economicValue: 7, socialValue: -8, isSelected: false },
  { id: '42', text: 'Competition drives innovation', economicValue: 8, socialValue: -4, isSelected: false },

  // Additional tiles to reach 49 total (7x7 grid)
  { id: '43', text: 'Technology solves problems', economicValue: 3, socialValue: -2, isSelected: false },
  { id: '44', text: 'Local communities know best', economicValue: -2, socialValue: -7, isSelected: false },
  { id: '45', text: 'Global cooperation is necessary', economicValue: -3, socialValue: 4, isSelected: false },
  { id: '46', text: 'Personal freedom has limits', economicValue: 2, socialValue: 6, isSelected: false },
  { id: '47', text: 'Economic growth benefits everyone', economicValue: 5, socialValue: 2, isSelected: false },
  { id: '48', text: 'Cultural diversity enriches society', economicValue: -4, socialValue: -3, isSelected: false },
  { id: '49', text: 'Efficiency should guide policy', economicValue: 4, socialValue: 3, isSelected: false }
];

// Return all tiles for a fixed 7x7 grid (49 tiles)
export function getAllTiles(): CompassTile[] {
  return compassTiles.map(tile => ({
    ...tile,
    isSelected: false
  }));
}

// Legacy function for compatibility - now returns all tiles
export function getBalancedTiles(count: number = 49): CompassTile[] {
  return getAllTiles();
}

// Legacy function for compatibility - now returns all tiles
export function getRandomTiles(count: number = 49): CompassTile[] {
  return getAllTiles();
}
