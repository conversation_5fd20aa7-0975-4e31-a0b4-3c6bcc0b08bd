export const politicalStatements = {
  libertarian: [
    "Taxation is theft",
    "The government that governs least governs best",
    "Drug prohibition should be ended",
    "Private property rights are sacred",
    "Free markets solve most problems",
    "Government surveillance violates privacy",
    "Victimless crimes shouldn't be crimes",
    "Voluntary exchange benefits everyone",
    "Regulations stifle innovation",
    "Individual liberty trumps collective security",
    "Abolish the minimum wage",
    "End corporate subsidies",
    "Privatize social security",
    "Open borders for free movement",
    "End the war on drugs"
  ],
  authoritarian: [
    "Strong leadership is necessary for order",
    "National security justifies surveillance",
    "Some speech should be restricted",
    "The state should guide moral behavior",
    "Order is more important than freedom",
    "Citizens should trust government experts",
    "Protest permits should be required",
    "Emergency powers are sometimes necessary",
    "Social media needs regulation",
    "Public safety over individual rights",
    "Mandatory military service builds character",
    "Government should control dangerous information",
    "Strong borders protect national identity",
    "Censorship prevents social harm",
    "Authority must be respected"
  ],
  left: [
    "Healthcare is a human right",
    "Wealth inequality is a major problem",
    "Workers should own the means of production",
    "Free education through college",
    "Tax the rich more heavily",
    "Universal basic income would help society",
    "Climate change requires government action",
    "Corporations have too much power",
    "Housing is a human right",
    "Unions protect workers",
    "Redistribute wealth for equality",
    "Public transportation should be free",
    "Nationalize essential industries",
    "Cancel student debt",
    "Medicare for All"
  ],
  right: [
    "Traditional values should be preserved",
    "Free markets create prosperity",
    "Personal responsibility over welfare",
    "Strong military protects freedom",
    "Lower taxes stimulate growth",
    "Merit-based immigration only",
    "Traditional family structures are best",
    "Capitalism lifts people out of poverty",
    "School choice improves education",
    "Deregulation helps businesses grow",
    "Patriotism should be taught in schools",
    "Religious freedom is fundamental",
    "Self-reliance builds character",
    "Competition drives innovation",
    "Respect for law and order"
  ],
  center: [
    "Compromise is better than extremism",
    "Both sides have valid points",
    "Moderate policies work best",
    "Pragmatism over ideology",
    "Balance individual and collective needs",
    "Reform is better than revolution",
    "Evidence-based policy making",
    "Incremental change is sustainable",
    "Bipartisan solutions are ideal",
    "Mixed economy works well",
    "Regulated capitalism is optimal",
    "Social safety net with work incentives",
    "Gradual environmental progress",
    "Reasonable immigration policies",
    "Balanced approach to civil liberties"
  ]
};

export function getRandomStatements(count: number = 25): string[] {
  const allStatements = Object.values(politicalStatements).flat();
  const shuffled = [...allStatements].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, count);
}

export function getBalancedStatements(count: number = 25): string[] {
  const statementsPerCategory = Math.floor(count / 5);
  const remainder = count % 5;
  
  const selected: string[] = [];
  const categories = Object.keys(politicalStatements) as Array<keyof typeof politicalStatements>;
  
  categories.forEach((category, index) => {
    const categoryStatements = [...politicalStatements[category]];
    const shuffled = categoryStatements.sort(() => Math.random() - 0.5);
    const take = statementsPerCategory + (index < remainder ? 1 : 0);
    selected.push(...shuffled.slice(0, take));
  });
  
  return selected.sort(() => Math.random() - 0.5);
}
