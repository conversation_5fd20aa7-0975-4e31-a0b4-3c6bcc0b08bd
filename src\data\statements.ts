import type { CompassTile, Quadrant } from '../types';

// Political Compass Quadrants
export const quadrants: Quadrant[] = [
  {
    name: "Authoritarian Left",
    color: "#dc3545", // Red
    description: "State control of economy, social restrictions",
    economicRange: [-10, 0],
    socialRange: [0, 10]
  },
  {
    name: "Authoritarian Right",
    color: "#007bff", // Blue
    description: "Free market, social restrictions",
    economicRange: [0, 10],
    socialRange: [0, 10]
  },
  {
    name: "Libertarian Left",
    color: "#28a745", // Green
    description: "Social programs, personal freedom",
    economicRange: [-10, 0],
    socialRange: [-10, 0]
  },
  {
    name: "Libertarian Right",
    color: "#ffc107", // Yellow
    description: "Free market, personal freedom",
    economicRange: [0, 10],
    socialRange: [-10, 0]
  }
];

// Political Compass Tiles with coordinate values
export const compassTiles: CompassTile[] = [
  // Libertarian Right Quadrant (Yellow) - Free market, personal freedom
  { id: '1', text: 'Taxation is theft', economicValue: 8, socialValue: -7, isSelected: false },
  { id: '2', text: 'Free markets solve most problems', economicValue: 7, socialValue: -5, isSelected: false },
  { id: '3', text: 'Drug prohibition should be ended', economicValue: 3, socialValue: -8, isSelected: false },
  { id: '4', text: 'Private property rights are sacred', economicValue: 6, socialValue: -4, isSelected: false },
  { id: '5', text: 'Government surveillance violates privacy', economicValue: 2, socialValue: -9, isSelected: false },
  { id: '6', text: 'Abolish the minimum wage', economicValue: 8, socialValue: -3, isSelected: false },

  // Libertarian Left Quadrant (Green) - Social programs, personal freedom
  { id: '7', text: 'Healthcare is a human right', economicValue: -6, socialValue: -4, isSelected: false },
  { id: '8', text: 'Universal basic income would help society', economicValue: -7, socialValue: -5, isSelected: false },
  { id: '9', text: 'Climate change requires government action', economicValue: -5, socialValue: -3, isSelected: false },
  { id: '10', text: 'Housing is a human right', economicValue: -8, socialValue: -6, isSelected: false },
  { id: '11', text: 'Free education through college', economicValue: -6, socialValue: -7, isSelected: false },
  { id: '12', text: 'Unions protect workers', economicValue: -4, socialValue: -5, isSelected: false },

  // Authoritarian Right Quadrant (Blue) - Free market, social restrictions
  { id: '13', text: 'Traditional values should be preserved', economicValue: 4, socialValue: 6, isSelected: false },
  { id: '14', text: 'Strong military protects freedom', economicValue: 5, socialValue: 7, isSelected: false },
  { id: '15', text: 'Merit-based immigration only', economicValue: 6, socialValue: 5, isSelected: false },
  { id: '16', text: 'Traditional family structures are best', economicValue: 3, socialValue: 8, isSelected: false },
  { id: '17', text: 'Patriotism should be taught in schools', economicValue: 4, socialValue: 7, isSelected: false },
  { id: '18', text: 'Respect for law and order', economicValue: 5, socialValue: 6, isSelected: false },

  // Authoritarian Left Quadrant (Red) - State control of economy, social restrictions
  { id: '19', text: 'Workers should own the means of production', economicValue: -8, socialValue: 4, isSelected: false },
  { id: '20', text: 'Nationalize essential industries', economicValue: -9, socialValue: 6, isSelected: false },
  { id: '21', text: 'Strong leadership is necessary for order', economicValue: -3, socialValue: 8, isSelected: false },
  { id: '22', text: 'The state should guide moral behavior', economicValue: -5, socialValue: 7, isSelected: false },
  { id: '23', text: 'National security justifies surveillance', economicValue: -4, socialValue: 9, isSelected: false },
  { id: '24', text: 'Redistribute wealth for equality', economicValue: -7, socialValue: 5, isSelected: false },

  // Center positions - Mixed views
  { id: '25', text: 'Compromise is better than extremism', economicValue: 0, socialValue: 0, isSelected: false }
];

export function getRandomTiles(count: number = 25): CompassTile[] {
  const shuffled = [...compassTiles].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, count).map((tile, index) => ({
    ...tile,
    id: `tile-${index}`,
    isSelected: false
  }));
}

export function getBalancedTiles(count: number = 25): CompassTile[] {
  // Ensure we get tiles from all quadrants
  const quadrantTiles = {
    authLeft: compassTiles.filter(t => t.economicValue < 0 && t.socialValue > 0),
    authRight: compassTiles.filter(t => t.economicValue > 0 && t.socialValue > 0),
    libLeft: compassTiles.filter(t => t.economicValue < 0 && t.socialValue < 0),
    libRight: compassTiles.filter(t => t.economicValue > 0 && t.socialValue < 0),
    center: compassTiles.filter(t => t.economicValue === 0 || t.socialValue === 0)
  };

  const tilesPerQuadrant = Math.floor(count / 4);
  const selected: CompassTile[] = [];

  Object.values(quadrantTiles).forEach(quadrant => {
    const shuffled = [...quadrant].sort(() => Math.random() - 0.5);
    selected.push(...shuffled.slice(0, tilesPerQuadrant));
  });

  // Fill remaining slots with random tiles
  const remaining = count - selected.length;
  if (remaining > 0) {
    const unusedTiles = compassTiles.filter(t => !selected.find(s => s.id === t.id));
    const shuffled = [...unusedTiles].sort(() => Math.random() - 0.5);
    selected.push(...shuffled.slice(0, remaining));
  }

  return selected.slice(0, count).map((tile, index) => ({
    ...tile,
    id: `tile-${index}`,
    isSelected: false
  }));
}
