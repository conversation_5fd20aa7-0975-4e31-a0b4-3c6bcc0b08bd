import { describe, it, expect } from 'vitest';
import { getBalancedStatements } from '../../data/statements';

// Mock localStorage for the tests
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: () => null,
    setItem: () => {},
    removeItem: () => {},
    clear: () => {},
  }
});

describe('Political Compass Bingo', () => {
  it('generates balanced statements', () => {
    const statements = getBalancedStatements(25);
    expect(statements).toHaveLength(25);
    expect(statements.every(s => typeof s === 'string')).toBe(true);
    expect(statements.every(s => s.length > 0)).toBe(true);
  });

  it('generates different statements on multiple calls', () => {
    const statements1 = getBalancedStatements(10);
    const statements2 = getBalancedStatements(10);

    // Should be different due to randomization
    const identical = statements1.every((stmt, index) => stmt === statements2[index]);
    expect(identical).toBe(false);
  });

  it('handles different grid sizes', () => {
    expect(getBalancedStatements(16)).toHaveLength(16);
    expect(getBalancedStatements(25)).toHaveLength(25);
    expect(getBalancedStatements(36)).toHaveLength(36);
  });
});
