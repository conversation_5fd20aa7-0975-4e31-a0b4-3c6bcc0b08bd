import type { CompassTile, UserPosition, Quadrant } from '../types';
import { quadrants } from '../data/statements';

/**
 * Calculate the user's political position based on selected tiles
 */
export function calculateUserPosition(selectedTiles: CompassTile[]): UserPosition {
  if (selectedTiles.length === 0) {
    return { economic: 0, social: 0 };
  }

  const totalEconomic = selectedTiles.reduce((sum, tile) => sum + tile.economicValue, 0);
  const totalSocial = selectedTiles.reduce((sum, tile) => sum + tile.socialValue, 0);

  const economic = Math.max(-10, Math.min(10, totalEconomic / selectedTiles.length));
  const social = Math.max(-10, Math.min(10, totalSocial / selectedTiles.length));

  return { economic, social };
}

/**
 * Determine which quadrant a position falls into
 */
export function getQuadrantForPosition(position: UserPosition): Quadrant {
  return quadrants.find(quadrant => 
    position.economic >= quadrant.economicRange[0] && 
    position.economic <= quadrant.economicRange[1] &&
    position.social >= quadrant.socialRange[0] && 
    position.social <= quadrant.socialRange[1]
  ) || quadrants[0]; // Default to first quadrant if none found
}

/**
 * Convert compass coordinates to pixel coordinates for visualization
 * Now properly accounts for grid boundaries and padding
 */
export function compassToPixelCoordinates(
  position: UserPosition,
  containerWidth: number,
  containerHeight: number
): { x: number; y: number } {
  // Add padding to keep dot within visible bounds
  const padding = 20;
  const effectiveWidth = containerWidth - (padding * 2);
  const effectiveHeight = containerHeight - (padding * 2);

  // Convert from compass coordinates (-10 to 10) to pixel coordinates
  // Economic: -10 (left) to +10 (right)
  // Social: +10 (top/authoritarian) to -10 (bottom/libertarian)
  const x = padding + ((position.economic + 10) / 20) * effectiveWidth;
  const y = padding + ((10 - position.social) / 20) * effectiveHeight;

  return { x, y };
}

/**
 * Get the background color for a tile based on its position
 */
export function getTileQuadrantColor(tile: CompassTile): string {
  const quadrant = getQuadrantForPosition({ 
    economic: tile.economicValue, 
    social: tile.socialValue 
  });
  return quadrant.color;
}

/**
 * Format position values for display
 */
export function formatPosition(position: UserPosition): string {
  return `Economic: ${position.economic.toFixed(1)}, Social: ${position.social.toFixed(1)}`;
}

/**
 * Get specific ideology name based on position coordinates
 */
export function getIdeologyName(position: UserPosition): string {
  const { economic, social } = position;

  // Center positions
  if (Math.abs(economic) <= 1 && Math.abs(social) <= 1) {
    return 'Centrist';
  }

  // Authoritarian Left (economic < 0, social > 0)
  if (economic < 0 && social > 0) {
    if (economic < -6 && social > 6) return 'Communist';
    if (economic < -4 && social > 4) return 'Socialist';
    if (economic < -2 && social > 2) return 'Social Democrat';
    return 'Progressive';
  }

  // Authoritarian Right (economic > 0, social > 0)
  if (economic > 0 && social > 0) {
    if (economic > 6 && social > 6) return 'Fascist';
    if (economic > 4 && social > 4) return 'Authoritarian Capitalist';
    if (economic > 2 && social > 2) return 'Conservative';
    return 'Moderate Conservative';
  }

  // Libertarian Left (economic < 0, social < 0)
  if (economic < 0 && social < 0) {
    if (economic < -6 && social < -6) return 'Anarcho-Communist';
    if (economic < -4 && social < -4) return 'Democratic Socialist';
    if (economic < -2 && social < -2) return 'Social Libertarian';
    return 'Liberal';
  }

  // Libertarian Right (economic > 0, social < 0)
  if (economic > 0 && social < 0) {
    if (economic > 6 && social < -6) return 'Anarcho-Capitalist';
    if (economic > 4 && social < -4) return 'Libertarian';
    if (economic > 2 && social < -2) return 'Classical Liberal';
    return 'Moderate Libertarian';
  }

  // Edge cases for pure economic or social positions
  if (Math.abs(economic) > Math.abs(social)) {
    return economic > 0 ? 'Economic Right' : 'Economic Left';
  } else {
    return social > 0 ? 'Authoritarian' : 'Libertarian';
  }
}

/**
 * Get a description of the user's political position
 */
export function getPositionDescription(position: UserPosition): string {
  const quadrant = getQuadrantForPosition(position);
  const economicDesc = position.economic > 2 ? 'strongly right' :
                      position.economic > 0 ? 'moderately right' :
                      position.economic < -2 ? 'strongly left' :
                      position.economic < 0 ? 'moderately left' : 'centrist';

  const socialDesc = position.social > 2 ? 'strongly authoritarian' :
                    position.social > 0 ? 'moderately authoritarian' :
                    position.social < -2 ? 'strongly libertarian' :
                    position.social < 0 ? 'moderately libertarian' : 'moderate';

  return `${quadrant.name} - ${economicDesc} economically, ${socialDesc} socially`;
}
