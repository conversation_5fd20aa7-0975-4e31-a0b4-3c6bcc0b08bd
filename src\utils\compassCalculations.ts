import type { CompassTile, UserPosition, Quadrant } from '../types';
import { quadrants } from '../data/statements';

/**
 * Calculate the user's political position based on selected tiles
 */
export function calculateUserPosition(selectedTiles: CompassTile[]): UserPosition {
  if (selectedTiles.length === 0) {
    return { economic: 0, social: 0 };
  }

  const totalEconomic = selectedTiles.reduce((sum, tile) => sum + tile.economicValue, 0);
  const totalSocial = selectedTiles.reduce((sum, tile) => sum + tile.socialValue, 0);

  const economic = Math.max(-10, Math.min(10, totalEconomic / selectedTiles.length));
  const social = Math.max(-10, Math.min(10, totalSocial / selectedTiles.length));

  return { economic, social };
}

/**
 * Determine which quadrant a position falls into
 */
export function getQuadrantForPosition(position: UserPosition): Quadrant {
  return quadrants.find(quadrant => 
    position.economic >= quadrant.economicRange[0] && 
    position.economic <= quadrant.economicRange[1] &&
    position.social >= quadrant.socialRange[0] && 
    position.social <= quadrant.socialRange[1]
  ) || quadrants[0]; // Default to first quadrant if none found
}

/**
 * Convert compass coordinates to pixel coordinates for visualization
 */
export function compassToPixelCoordinates(
  position: UserPosition,
  containerSize: number
): { x: number; y: number } {
  // Convert from compass coordinates (-10 to 10) to pixel coordinates (0 to containerSize)
  const x = ((position.economic + 10) / 20) * containerSize;
  const y = ((10 - position.social) / 20) * containerSize; // Invert Y axis (top = positive social)
  
  return { x, y };
}

/**
 * Get the background color for a tile based on its position
 */
export function getTileQuadrantColor(tile: CompassTile): string {
  const quadrant = getQuadrantForPosition({ 
    economic: tile.economicValue, 
    social: tile.socialValue 
  });
  return quadrant.color;
}

/**
 * Format position values for display
 */
export function formatPosition(position: UserPosition): string {
  return `Economic: ${position.economic.toFixed(1)}, Social: ${position.social.toFixed(1)}`;
}

/**
 * Get a description of the user's political position
 */
export function getPositionDescription(position: UserPosition): string {
  const quadrant = getQuadrantForPosition(position);
  const economicDesc = position.economic > 2 ? 'strongly right' : 
                      position.economic > 0 ? 'moderately right' :
                      position.economic < -2 ? 'strongly left' : 
                      position.economic < 0 ? 'moderately left' : 'centrist';
  
  const socialDesc = position.social > 2 ? 'strongly authoritarian' :
                    position.social > 0 ? 'moderately authoritarian' :
                    position.social < -2 ? 'strongly libertarian' :
                    position.social < 0 ? 'moderately libertarian' : 'moderate';
  
  return `${quadrant.name} - ${economicDesc} economically, ${socialDesc} socially`;
}
