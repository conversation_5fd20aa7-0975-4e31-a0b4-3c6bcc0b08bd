import React from 'react';
import type { CompassTile } from '../types';
import { getTileQuadrantColor } from '../utils/compassCalculations';
import './CompassTile.css';

interface CompassTileProps {
  tile: CompassTile;
  onClick: (tile: CompassTile) => void;
  style?: React.CSSProperties;
}

const CompassTileComponent: React.FC<CompassTileProps> = ({ tile, onClick, style }) => {
  const handleClick = () => {
    onClick(tile);
  };

  const backgroundColor = getTileQuadrantColor(tile);
  const isSelected = tile.isSelected;

  return (
    <button
      className={`compass-tile ${isSelected ? 'selected' : ''}`}
      onClick={handleClick}
      style={{
        backgroundColor: isSelected ? '#fff' : backgroundColor,
        color: isSelected ? '#333' : '#fff',
        opacity: isSelected ? 0.9 : 0.7,
        ...style // Apply grid positioning styles
      }}
      title={`Economic: ${tile.economicValue}, Social: ${tile.socialValue}`}
    >
      <span className="tile-text">{tile.text}</span>
      <div className="tile-coordinates">
        <span className="coord-economic">E: {tile.economicValue}</span>
        <span className="coord-social">S: {tile.socialValue}</span>
      </div>
    </button>
  );
};

export default CompassTileComponent;
