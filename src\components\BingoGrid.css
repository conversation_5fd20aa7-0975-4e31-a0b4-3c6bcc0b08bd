.bingo-grid-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.bingo-controls {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.control-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.control-button.reset {
  background: #f44336;
  color: white;
}

.control-button.reset:hover {
  background: #d32f2f;
  transform: translateY(-1px);
}

.control-button.new-card {
  background: #2196F3;
  color: white;
}

.control-button.new-card:hover {
  background: #1976D2;
  transform: translateY(-1px);
}

.control-button:active {
  transform: translateY(0);
}

.last-played {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-bottom: 15px;
  font-style: italic;
}

.bingo-grid {
  display: grid;
  gap: 2px;
  background: #333;
  padding: 2px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin: 0 auto;
  max-width: 100%;
}

.bingo-grid.size-4 {
  max-width: 600px;
}

.bingo-grid.size-5 {
  max-width: 700px;
}

.bingo-grid.size-6 {
  max-width: 800px;
}

.bingo-notification {
  margin-top: 20px;
  padding: 15px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  text-align: center;
  border-radius: 8px;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  animation: bingoAnimation 0.5s ease-out;
}

@keyframes bingoAnimation {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading {
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .bingo-grid-container {
    padding: 10px;
  }
  
  .bingo-grid {
    gap: 1px;
    padding: 1px;
  }
  
  .bingo-grid.size-4,
  .bingo-grid.size-5,
  .bingo-grid.size-6 {
    max-width: 100%;
  }
  
  .control-button {
    padding: 8px 16px;
    font-size: 12px;
    min-width: 100px;
  }
  
  .bingo-notification {
    font-size: 16px;
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .bingo-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .control-button {
    width: 200px;
  }
  
  .bingo-notification {
    font-size: 14px;
    padding: 10px;
  }
}
