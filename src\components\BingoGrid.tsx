import React, { useState, useEffect, useCallback } from 'react';
import { BingoSquare as BingoSquareComponent } from './BingoSquare';
import { BingoSquare, BingoCard, BingoLine } from '../types';
import { getBalancedStatements } from '../data/statements';
import { useLocalStorage } from '../hooks/useLocalStorage';
import './BingoGrid.css';

interface BingoGridProps {
  size?: number;
  onBingo?: (lines: BingoLine[]) => void;
}

export const BingoGrid: React.FC<BingoGridProps> = ({
  size = 5,
  onBingo
}) => {
  const [bingoCard, setBingoCard] = useLocalStorage<BingoCard | null>(`bingo-card-${size}`, null);
  const [lastPlayed, setLastPlayed] = useLocalStorage<string | null>(`bingo-last-played-${size}`, null);
  const [completedLines, setCompletedLines] = useState<BingoLine[]>([]);

  // Generate a new bingo card
  const generateCard = useCallback(() => {
    const statements = getBalancedStatements(size * size);
    const squares: BingoSquare[] = statements.map((text, index) => ({
      id: `square-${index}`,
      text,
      category: 'center', // We'll improve this categorization later
      isMarked: false
    }));

    const newCard: BingoCard = {
      id: `card-${Date.now()}`,
      squares,
      size
    };

    setBingoCard(newCard);
    setCompletedLines([]);
  }, [size]);

  // Initialize card on mount or when size changes
  useEffect(() => {
    if (!bingoCard || bingoCard.size !== size) {
      generateCard();
    }
  }, [generateCard, bingoCard, size]);

  // Update completed lines when card changes
  useEffect(() => {
    if (bingoCard) {
      const lines = checkForBingo(bingoCard.squares);
      setCompletedLines(lines);
    }
  }, [bingoCard, checkForBingo]);

  // Check for completed lines (rows, columns, diagonals)
  const checkForBingo = useCallback((squares: BingoSquare[]) => {
    const lines: BingoLine[] = [];
    
    // Check rows
    for (let row = 0; row < size; row++) {
      const rowSquares = [];
      let allMarked = true;
      
      for (let col = 0; col < size; col++) {
        const index = row * size + col;
        rowSquares.push(index);
        if (!squares[index].isMarked) {
          allMarked = false;
        }
      }
      
      if (allMarked) {
        lines.push({
          type: 'row',
          index: row,
          squares: rowSquares
        });
      }
    }

    // Check columns
    for (let col = 0; col < size; col++) {
      const colSquares = [];
      let allMarked = true;
      
      for (let row = 0; row < size; row++) {
        const index = row * size + col;
        colSquares.push(index);
        if (!squares[index].isMarked) {
          allMarked = false;
        }
      }
      
      if (allMarked) {
        lines.push({
          type: 'column',
          index: col,
          squares: colSquares
        });
      }
    }

    // Check main diagonal (top-left to bottom-right)
    const mainDiagSquares = [];
    let mainDiagAllMarked = true;
    for (let i = 0; i < size; i++) {
      const index = i * size + i;
      mainDiagSquares.push(index);
      if (!squares[index].isMarked) {
        mainDiagAllMarked = false;
      }
    }
    if (mainDiagAllMarked) {
      lines.push({
        type: 'diagonal',
        index: 0,
        squares: mainDiagSquares
      });
    }

    // Check anti-diagonal (top-right to bottom-left)
    const antiDiagSquares = [];
    let antiDiagAllMarked = true;
    for (let i = 0; i < size; i++) {
      const index = i * size + (size - 1 - i);
      antiDiagSquares.push(index);
      if (!squares[index].isMarked) {
        antiDiagAllMarked = false;
      }
    }
    if (antiDiagAllMarked) {
      lines.push({
        type: 'diagonal',
        index: 1,
        squares: antiDiagSquares
      });
    }

    return lines;
  }, [size]);

  // Handle square click
  const handleSquareClick = (squareId: string) => {
    if (!bingoCard) return;

    const updatedSquares = bingoCard.squares.map(square =>
      square.id === squareId
        ? { ...square, isMarked: !square.isMarked }
        : square
    );

    const updatedCard = { ...bingoCard, squares: updatedSquares };
    setBingoCard(updatedCard);
    setLastPlayed(new Date().toISOString());

    // Check for new bingo lines
    const newCompletedLines = checkForBingo(updatedSquares);
    
    // Find newly completed lines
    const newLines = newCompletedLines.filter(newLine => 
      !completedLines.some(existingLine => 
        existingLine.type === newLine.type && existingLine.index === newLine.index
      )
    );

    if (newLines.length > 0 && onBingo) {
      onBingo(newLines);
    }

    setCompletedLines(newCompletedLines);
  };

  // Reset all squares
  const resetCard = () => {
    if (!bingoCard) return;

    const hasMarkedSquares = bingoCard.squares.some(square => square.isMarked);

    if (hasMarkedSquares) {
      const confirmed = window.confirm(
        'Are you sure you want to reset all your selections? This action cannot be undone.'
      );
      if (!confirmed) return;
    }

    const resetSquares = bingoCard.squares.map(square => ({
      ...square,
      isMarked: false
    }));

    setBingoCard({ ...bingoCard, squares: resetSquares });
    setCompletedLines([]);
  };

  // Generate a completely new card
  const generateNewCard = () => {
    const hasMarkedSquares = bingoCard?.squares.some(square => square.isMarked);

    if (hasMarkedSquares) {
      const confirmed = window.confirm(
        'Are you sure you want to generate a new card? Your current progress will be lost.'
      );
      if (!confirmed) return;
    }

    generateCard();
  };

  // Format last played time
  const formatLastPlayed = (timestamp: string | null) => {
    if (!timestamp) return null;
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    return date.toLocaleDateString();
  };

  if (!bingoCard) {
    return <div className="loading">Generating bingo card...</div>;
  }

  return (
    <div className="bingo-grid-container">
      <div className="bingo-controls">
        <button onClick={resetCard} className="control-button reset">
          Reset Selections
        </button>
        <button onClick={generateNewCard} className="control-button new-card">
          New Card
        </button>
      </div>

      <div className="game-info">
        {lastPlayed && (
          <div className="last-played">
            Last played: {formatLastPlayed(lastPlayed)}
          </div>
        )}

        <div className="progress-stats">
          <span className="stat">
            {bingoCard.squares.filter(s => s.isMarked).length} / {bingoCard.squares.length} selected
          </span>
          {completedLines.length > 0 && (
            <span className="stat bingo-count">
              {completedLines.length} BINGO{completedLines.length > 1 ? 'S' : ''}!
            </span>
          )}
        </div>
      </div>
      
      <div 
        className={`bingo-grid size-${size}`}
        style={{
          gridTemplateColumns: `repeat(${size}, 1fr)`,
          gridTemplateRows: `repeat(${size}, 1fr)`
        }}
      >
        {bingoCard.squares.map((square, index) => (
          <BingoSquareComponent
            key={square.id}
            square={square}
            onClick={handleSquareClick}
            size={size <= 4 ? 'large' : size === 5 ? 'medium' : 'small'}
          />
        ))}
      </div>
      
      {completedLines.length > 0 && (
        <div className="bingo-notification">
          🎉 BINGO! You completed {completedLines.length} line{completedLines.length > 1 ? 's' : ''}!
        </div>
      )}
    </div>
  );
};
