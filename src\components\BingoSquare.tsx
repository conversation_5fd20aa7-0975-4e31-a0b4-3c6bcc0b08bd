import React from 'react';
import { BingoSquare as BingoSquareType } from '../types';
import './BingoSquare.css';

interface BingoSquareProps {
  square: BingoSquareType;
  onClick: (id: string) => void;
  size?: 'small' | 'medium' | 'large';
}

export const BingoSquare: React.FC<BingoSquareProps> = ({ 
  square, 
  onClick, 
  size = 'medium' 
}) => {
  const handleClick = () => {
    onClick(square.id);
  };

  return (
    <button
      className={`bingo-square ${square.isMarked ? 'marked' : ''} ${size}`}
      onClick={handleClick}
      aria-pressed={square.isMarked}
      title={square.text}
    >
      <span className="square-text">{square.text}</span>
      {square.isMarked && (
        <div className="checkmark">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>
        </div>
      )}
    </button>
  );
};
