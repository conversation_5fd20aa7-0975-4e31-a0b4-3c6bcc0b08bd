.compass-grid-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.compass-header {
  text-align: center;
  margin-bottom: 30px;
}

.compass-title {
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
}

.compass-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 20px;
}

.ideology-display {
  text-align: center;
  margin: 20px 0;
  padding: 20px 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.ideology-title {
  font-size: 2rem;
  font-weight: bold;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

.compass-position {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.position-item {
  text-align: center;
  padding: 15px 25px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 120px;
}

.position-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #4CAF50;
}

.position-label {
  font-size: 0.9rem;
  color: #666;
  margin-top: 5px;
}

.compass-wrapper {
  position: relative;
  margin: 30px auto;
  max-width: 800px;
}

.compass-grid {
  display: grid;
  gap: 2px;
  aspect-ratio: 1;
  background: #000;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  border: 3px solid #333;
}

.compass-grid.size-7 {
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(7, 1fr);
}

.compass-grid.size-5 {
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(5, 1fr);
}

.compass-axes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.axis-line {
  position: absolute;
  background: rgba(0, 0, 0, 0.3);
}

.axis-horizontal {
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  transform: translateY(-50%);
}

.axis-vertical {
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  transform: translateX(-50%);
}

.compass-labels {
  position: absolute;
  top: -40px;
  left: -40px;
  right: -40px;
  bottom: -40px;
  pointer-events: none;
  z-index: 5;
}

.axis-label {
  position: absolute;
  font-weight: bold;
  font-size: 0.9rem;
  color: #333;
  text-align: center;
}

.label-top {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.label-bottom {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.label-left {
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.label-right {
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  writing-mode: vertical-lr;
  text-orientation: mixed;
}

.user-position-dot {
  position: absolute;
  width: 20px;
  height: 20px;
  background: #ff0000;
  border: 4px solid #ffffff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5), 0 0 0 2px rgba(255, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.user-position-dot:hover {
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.6), 0 0 0 3px rgba(255, 0, 0, 0.4);
}

.compass-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
  flex-wrap: wrap;
}

.control-button {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.control-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #333;
}

.control-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.control-button:active {
  transform: translateY(0);
}

.position-description {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.description-title {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.description-text {
  font-size: 1rem;
  color: #666;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .compass-grid-container {
    padding: 15px;
  }

  .compass-title {
    font-size: 2rem;
  }

  .ideology-title {
    font-size: 1.6rem;
  }

  .compass-position {
    gap: 15px;
  }

  .position-item {
    padding: 12px 20px;
    min-width: 100px;
  }

  .position-value {
    font-size: 1.5rem;
  }

  .control-button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }

  .axis-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .ideology-display {
    padding: 15px 20px;
    margin: 15px 0;
  }

  .ideology-title {
    font-size: 1.4rem;
  }

  .compass-position {
    flex-direction: column;
    gap: 10px;
  }

  .compass-controls {
    flex-direction: column;
    align-items: center;
  }

  .control-button {
    width: 200px;
  }

  .user-position-dot {
    width: 16px;
    height: 16px;
    border-width: 3px;
  }
}
