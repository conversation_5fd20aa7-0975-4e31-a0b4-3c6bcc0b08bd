export interface CompassTile {
  id: string;
  text: string;
  economicValue: number; // -10 to +10 (left to right)
  socialValue: number;   // -10 to +10 (libertarian to authoritarian)
  isSelected: boolean;
}

export interface CompassGridData {
  id: string;
  tiles: CompassTile[];
  size: number;
}

export interface UserPosition {
  economic: number; // Average of selected tiles' economic values
  social: number;   // Average of selected tiles' social values
}

export interface Quadrant {
  name: string;
  color: string;
  description: string;
  economicRange: [number, number];
  socialRange: [number, number];
}
