.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

.app-header {
  text-align: center;
  padding: 2rem 1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  font-size: 2.5rem;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}

.app-description {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto 1rem auto;
  line-height: 1.6;
}

.app-instructions {
  background: rgba(255, 255, 255, 0.8);
  border-left: 4px solid #667eea;
  padding: 1rem;
  margin: 1rem auto;
  max-width: 600px;
  border-radius: 0 8px 8px 0;
  font-size: 0.95rem;
  color: #555;
}

.app-instructions h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.app-instructions ul {
  margin: 0;
  padding-left: 1.2rem;
}

.app-instructions li {
  margin-bottom: 0.3rem;
}

.app-main {
  flex: 1;
  padding: 2rem 1rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.app-footer {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-header {
    padding: 1.5rem 1rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .app-description {
    font-size: 1rem;
  }

  .app-main {
    padding: 1rem 0.5rem;
  }
}
