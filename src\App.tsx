import React from 'react'
import { BingoGrid } from './components/BingoGrid'
import { BingoLine } from './types'
import './App.css'

function App() {
  const handleBingo = (lines: BingoLine[]) => {
    console.log('Bingo achieved!', lines);
    // You can add more celebration logic here
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>Political Compass Bingo</h1>
        <p className="app-description">
          Explore different political perspectives through interactive gameplay!
        </p>

        <div className="app-instructions">
          <h3>How to Play:</h3>
          <ul>
            <li>Click on statements that align with your political views</li>
            <li>Try to complete a full row, column, or diagonal</li>
            <li>Your progress is automatically saved</li>
            <li>Generate new cards to explore different perspectives</li>
          </ul>
        </div>
      </header>

      <main className="app-main">
        <BingoGrid
          size={5}
          onBingo={handleBingo}
        />
      </main>

      <footer className="app-footer">
        <p>
          Political Compass Bingo - Explore different political perspectives through interactive gameplay
        </p>
      </footer>
    </div>
  )
}

export default App
