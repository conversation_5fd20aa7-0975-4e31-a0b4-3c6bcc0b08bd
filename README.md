# Political Compass Bingo

An interactive web-based Political Compass Bingo game built with React, TypeScript, and Vite. Explore different political perspectives through engaging gameplay!

## 🎯 Features

- **Interactive Bingo Grid**: Click on political statements that resonate with your views
- **Bingo Detection**: Automatic detection of completed rows, columns, and diagonals
- **Local Storage**: Your progress is automatically saved between sessions
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Modern UI**: Clean, intuitive interface with smooth animations
- **Balanced Content**: Statements covering libertarian, authoritarian, left, right, and centrist perspectives
- **Progress Tracking**: See your selection count and completed bingo lines
- **Card Generation**: Generate new random cards for varied gameplay

## 🚀 Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd political-compass-bingo
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## 🎮 How to Play

1. **Read the statements**: Each square contains a political statement or position
2. **Click to select**: Click on statements that align with your political views
3. **Aim for BINGO**: Try to complete a full row, column, or diagonal
4. **Track progress**: Monitor your selections and completed lines
5. **Generate new cards**: Click "New Card" to explore different statement combinations
6. **Reset anytime**: Use "Reset Selections" to clear your current progress

## 🛠️ Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm test` - Run tests

### Project Structure

```
src/
├── components/          # React components
│   ├── BingoGrid.tsx   # Main game grid component
│   ├── BingoSquare.tsx # Individual square component
│   └── __tests__/      # Component tests
├── data/               # Game data
│   └── statements.ts   # Political statements database
├── hooks/              # Custom React hooks
│   └── useLocalStorage.ts
├── types.ts            # TypeScript type definitions
└── App.tsx            # Main application component
```

## 🧪 Testing

The project includes unit tests for core functionality:

```bash
npm test
```

Tests cover:
- Statement generation and randomization
- Grid size handling
- Data validation

## 🎨 Customization

### Adding New Statements

Edit `src/data/statements.ts` to add new political statements:

```typescript
export const politicalStatements = {
  libertarian: [
    "Your new libertarian statement here",
    // ...
  ],
  // ... other categories
};
```

### Changing Grid Size

Modify the `size` prop in `App.tsx`:

```typescript
<BingoGrid size={4} /> // 4x4 grid
<BingoGrid size={5} /> // 5x5 grid (default)
<BingoGrid size={6} /> // 6x6 grid
```

## 🌐 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 📱 Mobile Support

The application is fully responsive and optimized for mobile devices with:
- Touch-friendly interface
- Responsive grid layout
- Mobile-optimized typography
- Gesture support

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Run the linter and tests
6. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- Built with [React](https://reactjs.org/) and [TypeScript](https://www.typescriptlang.org/)
- Powered by [Vite](https://vitejs.dev/) for fast development
- Styled with modern CSS and responsive design principles