.compass-tile {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px;
  font-weight: 700;
  text-align: center;
  min-height: 80px;
  position: relative;
  overflow: hidden;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9), -1px -1px 2px rgba(0, 0, 0, 0.7);
  background-clip: padding-box;
}

.compass-tile:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.compass-tile.selected {
  border: 4px solid #ffffff;
  font-weight: 900;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6), inset 0 0 0 2px rgba(255, 255, 255, 0.9);
  color: #ffffff;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 1), -2px -2px 4px rgba(0, 0, 0, 0.8);
}

.tile-text {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
  margin-bottom: 4px;
  word-break: break-word;
  hyphens: auto;
}

.tile-coordinates {
  display: flex;
  flex-direction: column;
  gap: 1px;
  font-size: 9px;
  opacity: 1;
  font-weight: normal;
}

.coord-economic,
.coord-social {
  background: rgba(0, 0, 0, 0.9);
  color: #ffffff;
  padding: 3px 6px;
  border-radius: 4px;
  font-family: monospace;
  font-weight: 900;
  text-shadow: none;
  border: 2px solid rgba(255, 255, 255, 0.8);
  font-size: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .compass-tile {
    font-size: 10px;
    padding: 6px;
    min-height: 70px;
  }
  
  .tile-coordinates {
    font-size: 8px;
  }
}

@media (max-width: 480px) {
  .compass-tile {
    font-size: 9px;
    padding: 4px;
    min-height: 60px;
  }
  
  .tile-coordinates {
    font-size: 7px;
  }
  
  .coord-economic,
  .coord-social {
    padding: 0px 2px;
  }
}
