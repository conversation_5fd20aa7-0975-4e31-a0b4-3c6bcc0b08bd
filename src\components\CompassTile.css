.compass-tile {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  min-height: 80px;
  position: relative;
  overflow: hidden;
  color: #000000;
  text-shadow: 0 0 3px rgba(255, 255, 255, 0.8);
}

.compass-tile:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.compass-tile.selected {
  border: 4px solid #000000;
  font-weight: bold;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), inset 0 0 0 2px rgba(255, 255, 255, 0.8);
  color: #000000;
  text-shadow: 0 0 4px rgba(255, 255, 255, 1);
}

.tile-text {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
  margin-bottom: 4px;
  word-break: break-word;
  hyphens: auto;
}

.tile-coordinates {
  display: flex;
  flex-direction: column;
  gap: 1px;
  font-size: 9px;
  opacity: 1;
  font-weight: normal;
}

.coord-economic,
.coord-social {
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
  font-weight: bold;
  text-shadow: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .compass-tile {
    font-size: 10px;
    padding: 6px;
    min-height: 70px;
  }
  
  .tile-coordinates {
    font-size: 8px;
  }
}

@media (max-width: 480px) {
  .compass-tile {
    font-size: 9px;
    padding: 4px;
    min-height: 60px;
  }
  
  .tile-coordinates {
    font-size: 7px;
  }
  
  .coord-economic,
  .coord-social {
    padding: 0px 2px;
  }
}
