.bingo-square {
  position: relative;
  border: 2px solid #333;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  min-height: 80px;
  overflow: hidden;
  word-wrap: break-word;
  hyphens: auto;
}

.bingo-square:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.bingo-square:active {
  transform: translateY(0);
}

.bingo-square.marked {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border-color: #45a049;
  animation: markAnimation 0.3s ease-out;
}

.bingo-square.marked:hover {
  background: linear-gradient(135deg, #45a049, #388e3c);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

@keyframes markAnimation {
  0% {
    transform: scale(0.9);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.square-text {
  position: relative;
  z-index: 1;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.checkmark {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4CAF50;
}

.checkmark svg {
  width: 14px;
  height: 14px;
}

/* Size variants */
.bingo-square.small {
  min-height: 60px;
  font-size: 10px;
  padding: 4px;
}

.bingo-square.medium {
  min-height: 80px;
  font-size: 12px;
  padding: 8px;
}

.bingo-square.large {
  min-height: 100px;
  font-size: 14px;
  padding: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .bingo-square {
    font-size: 10px;
    min-height: 60px;
    padding: 4px;
  }
  
  .bingo-square.large {
    font-size: 11px;
    min-height: 70px;
    padding: 6px;
  }
  
  .checkmark {
    width: 16px;
    height: 16px;
    top: 2px;
    right: 2px;
  }
  
  .checkmark svg {
    width: 12px;
    height: 12px;
  }
}
