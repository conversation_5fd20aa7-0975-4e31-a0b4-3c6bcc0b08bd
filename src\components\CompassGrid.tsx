import React, { useState, useEffect, useCallback } from 'react';
import type { CompassGridData, CompassTile, UserPosition } from '../types';
import { getBalancedTiles } from '../data/statements';
import { useLocalStorage } from '../hooks/useLocalStorage';
import { calculateUserPosition, compassToPixelCoordinates, getPositionDescription } from '../utils/compassCalculations';
import CompassTileComponent from './CompassTile';
import './CompassGrid.css';

interface CompassGridProps {
  size?: number;
}

export const CompassGrid: React.FC<CompassGridProps> = ({
  size = 5
}) => {
  const [compassGrid, setCompassGrid] = useLocalStorage<CompassGridData | null>(`compass-grid-${size}`, null);
  const [lastPlayed, setLastPlayed] = useLocalStorage<string | null>(`compass-last-played-${size}`, null);
  const [userPosition, setUserPosition] = useState<UserPosition>({ economic: 0, social: 0 });

  // Generate a new compass grid
  const generateGrid = useCallback(() => {
    const tiles = getBalancedTiles(size * size);

    const newGrid: CompassGridData = {
      id: `grid-${Date.now()}`,
      tiles,
      size
    };

    setCompassGrid(newGrid);
    setUserPosition({ economic: 0, social: 0 });
  }, [size, setCompassGrid]);

  // Handle tile selection
  const handleTileClick = useCallback((clickedTile: CompassTile) => {
    if (!compassGrid) return;

    const updatedTiles = compassGrid.tiles.map(tile =>
      tile.id === clickedTile.id
        ? { ...tile, isSelected: !tile.isSelected }
        : tile
    );

    const updatedGrid = { ...compassGrid, tiles: updatedTiles };
    setCompassGrid(updatedGrid);
    setLastPlayed(new Date().toISOString());

    // Calculate new user position
    const selectedTiles = updatedTiles.filter(tile => tile.isSelected);
    const newPosition = calculateUserPosition(selectedTiles);
    setUserPosition(newPosition);
  }, [compassGrid, setCompassGrid, setLastPlayed]);

  // Reset all selections
  const resetSelections = useCallback(() => {
    if (!compassGrid) return;

    const hasSelectedTiles = compassGrid.tiles.some(tile => tile.isSelected);
    if (hasSelectedTiles) {
      const confirmed = window.confirm(
        'Are you sure you want to reset all selections? Your current position will be lost.'
      );
      if (!confirmed) return;
    }

    const resetTiles = compassGrid.tiles.map(tile => ({
      ...tile,
      isSelected: false
    }));

    setCompassGrid({ ...compassGrid, tiles: resetTiles });
    setUserPosition({ economic: 0, social: 0 });
  }, [compassGrid, setCompassGrid]);

  // Generate new grid
  const generateNewGrid = useCallback(() => {
    const hasSelectedTiles = compassGrid?.tiles.some(tile => tile.isSelected);
    if (hasSelectedTiles) {
      const confirmed = window.confirm(
        'Are you sure you want to generate a new grid? Your current progress will be lost.'
      );
      if (!confirmed) return;
    }
    generateGrid();
  }, [compassGrid, generateGrid]);

  // Initialize grid on mount or when size changes
  useEffect(() => {
    if (!compassGrid || compassGrid.size !== size) {
      generateGrid();
    }
  }, [compassGrid, size, generateGrid]);

  // Calculate user position when grid changes
  useEffect(() => {
    if (compassGrid) {
      const selectedTiles = compassGrid.tiles.filter(tile => tile.isSelected);
      const newPosition = calculateUserPosition(selectedTiles);
      setUserPosition(newPosition);
    }
  }, [compassGrid]);

  if (!compassGrid) {
    return <div className="loading">Loading Political Compass...</div>;
  }

  const selectedCount = compassGrid.tiles.filter(tile => tile.isSelected).length;
  const positionDescription = getPositionDescription(userPosition);

  // Calculate pixel coordinates for the user position dot
  const gridElement = document.querySelector('.compass-grid');
  const containerSize = gridElement ? gridElement.clientWidth : 800;
  const dotPosition = compassToPixelCoordinates(userPosition, containerSize);

  return (
    <div className="compass-grid-container">
      <div className="compass-header">
        <h1 className="compass-title">Political Compass</h1>
        <p className="compass-subtitle">
          Click on statements that align with your political views to see your position on the compass
        </p>
      </div>

      <div className="compass-position">
        <div className="position-item">
          <div className="position-value">{userPosition.economic.toFixed(1)}</div>
          <div className="position-label">Economic</div>
        </div>
        <div className="position-item">
          <div className="position-value">{userPosition.social.toFixed(1)}</div>
          <div className="position-label">Social</div>
        </div>
        <div className="position-item">
          <div className="position-value">{selectedCount}</div>
          <div className="position-label">Selected</div>
        </div>
      </div>

      <div className="compass-wrapper">
        <div className="compass-labels">
          <div className="axis-label label-top">Authoritarian (+10)</div>
          <div className="axis-label label-bottom">Libertarian (-10)</div>
          <div className="axis-label label-left">Left (-10)</div>
          <div className="axis-label label-right">Right (+10)</div>
        </div>

        <div className={`compass-grid size-${size}`}>
          {compassGrid.tiles.map((tile) => (
            <CompassTileComponent
              key={tile.id}
              tile={tile}
              onClick={handleTileClick}
            />
          ))}
          
          <div className="compass-axes">
            <div className="axis-line axis-horizontal"></div>
            <div className="axis-line axis-vertical"></div>
          </div>

          {selectedCount > 0 && (
            <div 
              className="user-position-dot"
              style={{
                left: `${dotPosition.x}px`,
                top: `${dotPosition.y}px`
              }}
              title={`Your position: Economic ${userPosition.economic.toFixed(1)}, Social ${userPosition.social.toFixed(1)}`}
            />
          )}
        </div>
      </div>

      {selectedCount > 0 && (
        <div className="position-description">
          <div className="description-title">Your Political Position</div>
          <div className="description-text">{positionDescription}</div>
        </div>
      )}

      <div className="compass-controls">
        <button 
          className="control-button secondary" 
          onClick={resetSelections}
          disabled={selectedCount === 0}
        >
          Reset Selections
        </button>
        <button 
          className="control-button primary" 
          onClick={generateNewGrid}
        >
          New Grid
        </button>
      </div>

      {lastPlayed && (
        <div className="game-info">
          <div className="last-played">
            Last updated: {new Date(lastPlayed).toLocaleString()}
          </div>
        </div>
      )}
    </div>
  );
};


